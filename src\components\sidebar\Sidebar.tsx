'use client';

import React from 'react';
import { useAuthContext } from '../AuthProvider';
import SidebarBrandHeader from './SidebarBrandHeader';
import SidebarMembershipPromo from './SidebarMembershipPromo';
import SidebarJourneyProgress from './SidebarJourneyProgress';
import SidebarCurrencyDisplay from './SidebarCurrencyDisplay';
import SidebarStaminaDisplay from './SidebarStaminaDisplay';
import SidebarStreakDisplay from './SidebarStreakDisplay';
import SidebarNavigation from './SidebarNavigation';
import SidebarRecentChats from './SidebarRecentChats';
import SidebarCommunity from './SidebarCommunity';
import SidebarLinks from './SidebarLinks';
import SidebarUserInfo from './SidebarUserInfo';

interface SidebarProps {
  lang: string;
}

const Sidebar: React.FC<SidebarProps> = ({ lang }) => {
  const { user } = useAuthContext();

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Brand Header - Special padding - Fixed at top */}
      <SidebarBrandHeader />

      {/* Separator to prevent content overlap during scroll */}
      <div className="bg-white dark:bg-black"></div>

      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        {/* User Currencies - handled internally with responsive width */}
        <div className="py-2">
          <SidebarCurrencyDisplay user={user} lang={lang} />
        </div>

        {/* Stamina Display - 16px horizontal, 8px vertical */}
        <div className="px-4 py-2">
          <SidebarStaminaDisplay user={user} lang={lang} />
        </div>

        {/* Interaction Streak - 16px horizontal, 8px vertical */}
        <div className="px-4 py-2">
          <SidebarStreakDisplay user={user} lang={lang} />
        </div>

        {/* Today's Journey - has its own padding */}
        <SidebarJourneyProgress lang={lang} />

        {/* Recent Chats - has its own padding */}
        <SidebarRecentChats lang={lang} />

        {/* Function Navigation - has its own padding */}
        <SidebarNavigation lang={lang} />

        {/* Community - has its own padding */}
        <SidebarCommunity lang={lang} />

        {/* Useful Links - has its own padding */}
        <SidebarLinks lang={lang} />

        {/* Membership Promo - 16px horizontal, 8px vertical */}
        <div className="px-4 py-2">
          <SidebarMembershipPromo lang={lang} />
        </div>
      </div>

      {/* Bottom Section - User Info - Fixed at bottom */}
      <SidebarUserInfo lang={lang} />
    </div>
  );
};

export default Sidebar;
