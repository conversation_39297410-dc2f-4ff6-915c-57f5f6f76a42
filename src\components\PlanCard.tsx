'use client';

import React from 'react';
import { Crown, Check } from 'lucide-react';

interface PlanCardProps {
  name: string;
  price: string | number;
  period: string;
  originalPrice?: string;
  discount?: string;
  seats: string;
  features: string[];
  isPopular?: boolean;
  isCurrent?: boolean;
  buttonType: 'cancel' | 'current' | 'upgrade';
  buttonText?: string;
  popularText?: string;
}

const PlanCard: React.FC<PlanCardProps> = ({
  name,
  price,
  period,
  originalPrice,
  discount,
  seats,
  features,
  isPopular = false,
  isCurrent = false,
  buttonType,
  buttonText,
  popularText = 'Most Popular'
}) => {
  const getButtonText = () => {
    if (buttonText) return buttonText;
    switch (buttonType) {
      case 'cancel':
        return 'Cancel At Next Billing Cycle';
      case 'current':
        return 'Currently Active';
      case 'upgrade':
        return 'Subscribe Now';
      default:
        return 'Subscribe Now';
    }
  };

  const getButtonStyle = () => {
    switch (buttonType) {
      case 'cancel':
        return 'bg-red-50 text-red-700 hover:bg-red-100 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/30 transform hover:scale-[1.02] active:scale-[0.98]';
      case 'current':
        return 'bg-green-100 text-green-800 cursor-not-allowed dark:bg-green-900/30 dark:text-green-400';
      case 'upgrade':
        return 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:shadow-lg hover:shadow-current/30 transform hover:scale-[1.02] active:scale-[0.98]';
      default:
        return 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:shadow-lg hover:shadow-current/30 transform hover:scale-[1.02] active:scale-[0.98]';
    }
  };

  return (
    <div className={`relative bg-card text-card-foreground rounded-2xl p-8 border shadow-sm hover:shadow-lg theme-transition h-full ${
      isPopular 
        ? 'border-primary ring-2 ring-primary/20 shadow-primary/10' 
        : 'border-border hover:border-primary/30'
    }`}>
      {isPopular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <div className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground px-6 py-2 rounded-full text-sm font-medium shadow-lg">
            ✨ {popularText}
          </div>
        </div>
      )}
      
      {isCurrent && (
        <div className="absolute -top-4 right-6">
          <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center shadow-lg">
            <Crown size={14} className="mr-1" />
            Current
          </div>
        </div>
      )}

      <div className="space-y-6 h-full flex flex-col">
        {/* Plan Name */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-card-foreground mb-2">{name}</h3>
          <p className="text-sm text-card-foreground/60 bg-card-foreground/5 px-3 py-1 rounded-full inline-block">{seats}</p>
        </div>

        {/* Price */}
        <div className="space-y-2 text-center">
          <div className="flex items-baseline justify-center space-x-2">
            <span className="text-4xl font-bold text-primary">
              {price === 'Free' ? 'Free' : price}
            </span>
            {price !== 'Free' && (
              <span className="text-card-foreground/60 text-lg">/{period}</span>
            )}
          </div>
          
          {originalPrice && (
            <div className="flex items-center justify-center space-x-2">
              <span className="text-sm text-card-foreground/40 line-through">
                {originalPrice}
              </span>
              {discount && (
                <span className="text-sm bg-gradient-to-r from-red-100 to-red-50 dark:from-red-900/30 dark:to-red-800/20 text-red-700 dark:text-red-300 px-3 py-1 rounded-full font-medium border border-red-200/50 dark:border-red-700/30">
                  {discount}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Features */}
        <div className="space-y-4 flex-1">
          {features.map((feature, index) => (
            <div key={index} className="flex items-start space-x-3">
              <div className="bg-emerald-100 dark:bg-emerald-900/30 rounded-full p-1 mt-0.5 flex-shrink-0">
                <Check size={12} className="text-emerald-600 dark:text-emerald-400" />
              </div>
              <span className="text-sm text-card-foreground/80 leading-relaxed">{feature}</span>
            </div>
          ))}
        </div>

        {/* Button */}
        <button
          className={`w-full py-4 px-6 rounded-xl text-sm font-semibold transition-all duration-200 ${getButtonStyle()}`}
          disabled={buttonType === 'current'}
        >
          {getButtonText()}
        </button>
      </div>
    </div>
  );
};

export default PlanCard; 