import StaminaClientPage from './StaminaClientPage';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n';

export default async function StaminaPage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const { t } = await useTranslation(lang, 'translation');

  return (
    <MainAppLayout lang={lang} title={t('stamina.title')}>
      <div className="min-h-screen bg-gray-50/50 dark:bg-gray-900/50">
        <StaminaClientPage lang={lang} />
      </div>
    </MainAppLayout>
  );
}
