# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build & Development
- `npm run dev` - Start development server with network binding (accessible on all interfaces)
- `npm run build` - Build the production application
- `npm run start` - Start the production server

### Code Quality
- `npm run lint` - Run Biome linter with auto-fix and TypeScript type checking
- `npm run format` - Format code using Biome

### Testing
- Run specific test: Use standard Jest patterns (check for test files in `__tests__` directories)
- The project uses TypeScript with strict mode enabled

## Architecture Overview

### Framework & Core Technologies
- **Next.js 15** with App Router architecture
- **TypeScript** with strict configuration
- **Tailwind CSS** with custom romantic purple-pink color scheme
- **Biome** for linting and formatting (replaces ESLint + Prettier)
- **Shadcn/ui** component library with custom configuration

### Internationalization (i18n)
- **Languages**: English (default), Chinese, Japanese
- **Implementation**: i18next with react-i18next
- **URL Structure**: `/[lang]/page` pattern (e.g., `/en/dashboard`, `/zh/profile`)
- **Font Loading**: Language-specific fonts loaded dynamically:
  - English: Quicksand (primary)
  - Japanese: JK Maru Gothic (primary) + Quicksand (fallback)
  - Chinese: Lansui (primary) + Quicksand (fallback)
- **Middleware**: Automatic locale detection and redirection
- **Translation Files**: Located in `src/app/i18n/locales/[lang]/translation.json`

### Project Structure
```
src/
├── app/[lang]/                 # Internationalized pages (App Router)
│   ├── auth/                   # Authentication pages
│   ├── character/              # Character management
│   ├── chats/                  # Chat interface
│   ├── create-character/       # Character creation flow
│   ├── create-story/           # Story creation flow
│   └── ...                     # Other feature pages
├── components/                 # Reusable UI components
│   ├── character-creation/     # Character creation components
│   ├── story-creation/         # Story creation components
│   ├── common/                 # Shared UI components
│   ├── sidebar/                # Sidebar navigation
│   └── ui/                     # Base UI components (Shadcn)
├── hooks/                      # Custom React hooks
├── lib/                        # Utility functions and API clients
├── contexts/                   # React context providers
└── types/                      # TypeScript type definitions
```

### Key Architectural Patterns

#### Layout System
- Root layout (`src/app/layout.tsx`): Basic HTML structure
- Language layout (`src/app/[lang]/layout.tsx`): i18n provider, font loading, theme provider
- Main app layout (`src/components/MainAppLayout.tsx`): Responsive header, sidebar, bottom nav

#### Responsive Design
- **Desktop**: Sidebar + main content
- **Mobile**: Bottom navigation + collapsible sidebar overlay
- **Breakpoints**: Tailwind's default system with custom 3xl (2560px) breakpoint

#### State Management
- React Context for global state (Auth, Notifications, Store)
- Local component state with custom hooks
- No external state management library used

#### Component Architecture
- Feature-based component organization
- Shared components in `/common` directory
- Custom hooks for complex logic extraction
- TypeScript interfaces in `/types` directory

#### Theme System
- CSS variables for dynamic theming
- Custom romantic color palette (purple-pink gradients)
- Dark/light mode support via next-themes
- Language-specific font families

### API Integration
- API clients in `src/lib/` directory
- Mock data files for development
- Authentication API handling
- Store/commerce integration

### Important Configuration Details
- **TypeScript paths**: `@/*` alias maps to `src/*`
- **Next.js config**: Image optimization disabled, canvas externalized
- **Biome config**: Strict linting with disabled accessibility rules
- **Build errors ignored**: TypeScript and ESLint errors don't fail builds (development setup)

### Development Workflow Notes
- Use Bun as package manager (bun.lock present)
- Development server binds to all interfaces for network access
- Components follow Shadcn/ui patterns with custom styling
- Multi-language development requires testing across all supported locales