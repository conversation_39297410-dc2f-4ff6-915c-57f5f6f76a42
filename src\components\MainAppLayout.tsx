'use client';

import React, { useState, useEffect, useRef } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import { i18n } from '@/i18n-config';
import SearchOverlay from './SearchOverlay';
import BottomNavBar from './BottomNavBar';
import ResponsiveHeader from './common/ResponsiveHeader';
import { Sidebar } from './sidebar';
import { useAuthContext } from './AuthProvider';
import { NotificationProvider } from '@/contexts/NotificationContext';
import NotificationCenter from './notifications/NotificationCenter';
import ToastNotifications from './notifications/ToastNotifications';

interface MainAppLayoutProps {
  children: React.ReactNode;
  lang: string;
  title?: string;
}

function classNames(...classes: (string | boolean)[]) {
  return classes.filter(Boolean).join(' ');
}

const MainAppLayout: React.FC<MainAppLayoutProps> = ({ children, lang, title }) => {
  const pathname = usePathname();
  const router = useRouter();
  const { user } = useAuthContext();

  const { t } = useTranslation(lang, 'translation');

  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);



  React.useEffect(() => {
    const checkScreenSize = () => {
      const isLargeScreen = window.innerWidth >= 1024;
      setIsSidebarOpen(isLargeScreen);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  React.useEffect(() => {
    if (window.innerWidth < 1024) {
      setIsSidebarOpen(false);
    }
  }, [pathname]);





  return (
    <NotificationProvider>
      <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-50">
      <div className="flex-1 flex overflow-hidden">
        {/* Desktop Sidebar */}
        <aside className="hidden lg:flex w-80 flex-col bg-white dark:bg-black border-r border-gray-200 dark:border-gray-800">
          <Sidebar lang={lang} />
        </aside>
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-y-auto pb-20 md:pb-0">
          {/* Unified Responsive Header */}
          <ResponsiveHeader
            lang={lang}
            onSearchClick={() => setIsSearchOpen(true)}
            title={title}
            notificationCenter={<NotificationCenter />}
          />

          <main className="w-full">
            {children}
          </main>
        </div>

        {/* Mobile Sidebar overlay */}
        {isSidebarOpen && (
            <div className="lg:hidden fixed inset-0 z-40 bg-black/30" onClick={() => setIsSidebarOpen(false)}></div>
        )}
        <aside
          className={classNames(
            "lg:hidden fixed top-0 left-0 z-50 h-full w-80 bg-white dark:bg-black shadow-lg transition-transform duration-300 ease-in-out",
            isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
          )}
        >
          <Sidebar lang={lang} />
        </aside>
      </div>

      {/* Bottom Nav Bar (Mobile) */}
      <BottomNavBar lang={lang} />

      {/* Notification Components */}
      <ToastNotifications />

      {isSearchOpen && <SearchOverlay isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} lang={lang} />}
      </div>
    </NotificationProvider>
  );
};

export default MainAppLayout;
