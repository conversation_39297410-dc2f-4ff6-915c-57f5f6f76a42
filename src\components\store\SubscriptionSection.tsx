'use client';

import React, { useState } from 'react';
import { Crown, Check, Star, Sparkles, ArrowRight } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface Plan {
  name: string;
  price: number;
  period: string;
  status: string;
  features: string[];
}

interface SubscriptionData {
  currentPlans: {
    standard: { isActive: boolean; expiresAt: string | null };
    pass: { isActive: boolean; expiresAt: string | null };
    diamond: { isActive: boolean; expiresAt: string | null };
    metaverse: { isActive: boolean; expiresAt: string | null };
  };
  plans: {
    standard: Plan;
    pass: Plan;
    diamond: Plan;
    metaverse: Plan;
  };
}

interface SubscriptionSectionProps {
  lang: string;
  featured?: boolean;
  subscriptionData?: SubscriptionData;
}

const SubscriptionSection: React.FC<SubscriptionSectionProps> = ({
  lang,
  featured = false,
  subscriptionData
}) => {
  const { t } = useTranslation(lang, 'translation');

  // Default subscription data with SOTA price progression ordering
  const defaultSubscriptionData: SubscriptionData = {
    currentPlans: {
      standard: { isActive: true, expiresAt: null },
      pass: { isActive: false, expiresAt: null },
      diamond: { isActive: false, expiresAt: null },
      metaverse: { isActive: false, expiresAt: null }
    },
    plans: {
      standard: {
        name: 'Standard',
        price: 0,
        period: 'forever',
        status: 'permanent',
        features: [
          '5 AI conversations daily',
          'Basic character library access',
          'Standard quality avatars',
          'Community features'
        ]
      },
      pass: {
        name: 'Pass',
        price: 9.99,
        period: 'month',
        status: 'inactive',
        features: [
          'Unlimited AI conversations',
          'Full character library access',
          'High-definition avatars',
          'Priority customer support',
          'Exclusive badges',
          'Advanced customization options'
        ]
      },
      diamond: {
        name: 'Diamond',
        price: 29.99,
        period: 'month',
        status: 'inactive',
        features: [
          'All Pass features included',
          'Exclusive diamond characters',
          '4K ultra-HD avatars',
          'Private chat rooms',
          'Early access to new features',
          'Dedicated account manager',
          'Unlimited storage space'
        ]
      },
      metaverse: {
        name: 'MetaVerse Pass',
        price: 99.99,
        period: 'month',
        status: 'inactive',
        features: [
          'All Diamond features included',
          'MetaVerse world access',
          'Virtual reality integration',
          'Custom avatar creation',
          'Exclusive MetaVerse events',
          'Cross-platform synchronization',
          'Advanced AI interactions',
          'Virtual property ownership',
          'Premium MetaVerse content'
        ]
      }
    }
  };

  const data = subscriptionData || defaultSubscriptionData;
  const [planStates, setPlanStates] = useState(data.currentPlans);

  const handleSubscriptionToggle = (planType: 'pass' | 'diamond' | 'metaverse') => {
    setPlanStates(prev => ({
      ...prev,
      [planType]: {
        ...prev[planType],
        isActive: !prev[planType].isActive,
        expiresAt: !prev[planType].isActive ? '2024-12-31' : null
      }
    }));
  };

  // If featured mode, show simplified view with SOTA ordering
  if (featured) {
    const plans = [
      {
        id: 'diamondPass',
        name: 'Diamond Pass',
        shortName: 'Diamond',
        description: 'Premium experience with exclusive content and dedicated support',
        price: '$29.99',
        period: 'month',
        features: [
          'All Pass features included',
          'Exclusive diamond characters',
          '4K ultra-HD avatars',
          'Private chat rooms',
          'Early access to new features'
        ],
        popular: true,
        color: 'from-purple-500 to-pink-600',
        icon: Crown,
      },
      {
        id: 'metaversePass',
        name: 'MetaVerse Pass',
        shortName: 'MetaVerse',
        description: 'Ultimate virtual reality experience with exclusive MetaVerse access',
        price: '$99.99',
        period: 'month',
        features: [
          'All Diamond features included',
          'MetaVerse world access',
          'Virtual reality integration',
          'Custom avatar creation',
          'Exclusive MetaVerse events'
        ],
        popular: false,
        color: 'from-cyan-500 to-blue-600',
        icon: Sparkles,
      },
    ];

    return (
      <div className="space-y-6">

        {/* Featured Plan - SOTA Responsive Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-6">
          {plans.map((plan) => {
            const Icon = plan.icon;
            return (
              <div
                key={plan.id}
                className="relative bg-gray-50 dark:bg-gray-700/50 rounded-xl p-6 border border-purple-300 dark:border-purple-600 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 transition-all duration-200 hover:shadow-lg"
              >
                <div className="absolute -top-3 left-4">
                  <div className="flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                    <Star className="w-3 h-3" fill="currentColor" />
                    {t('store.subscriptions.mostPopular')}
                  </div>
                </div>

                <div className="flex items-start gap-4 mb-6">
                  <div className={`w-12 h-12 bg-gradient-to-br ${plan.color} rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                      {plan.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                      {plan.description}
                    </p>
                    <div className="flex items-baseline gap-1">
                      <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {plan.price}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        /{plan.period}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <ul className="space-y-2">
                    {plan.features.slice(0, 3).map((feature, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="w-4 h-4 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
                        </div>
                        <span className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                          {feature}
                        </span>
                      </li>
                    ))}
                    {plan.features.length > 3 && (
                      <li className="text-center pt-2">
                        <span className="text-gray-500 dark:text-gray-400 text-sm font-medium">
                          +{plan.features.length - 3} more features
                        </span>
                      </li>
                    )}
                  </ul>
                </div>

                <button
                  className={`w-full py-3 px-4 rounded-xl font-semibold text-white bg-gradient-to-r ${plan.color} hover:shadow-lg hover:shadow-current/30 transition-all duration-200 flex items-center justify-center gap-2`}
                >
                  <Crown className="w-4 h-4" />
                  {t('store.subscriptions.upgrade')}
                </button>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // Full subscription management view
  return (

      <div className="space-y-8">

      {/* Active Plans Status */}
      {(planStates.pass.isActive || planStates.diamond.isActive || planStates.metaverse.isActive) && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <Crown className="text-green-600 dark:text-green-400" size={24} />
            <div>
              <h3 className="font-semibold text-green-800 dark:text-green-200">
                Active Subscriptions:
              </h3>
              <div className="text-sm text-green-600 dark:text-green-400 space-y-1.5">
                {planStates.pass.isActive && (
                  <div>Pass - Expires: {planStates.pass.expiresAt}</div>
                )}
                {planStates.diamond.isActive && (
                  <div>Diamond - Expires: {planStates.diamond.expiresAt}</div>
                )}
                {planStates.metaverse.isActive && (
                  <div>MetaVerse Pass - Expires: {planStates.metaverse.expiresAt}</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* SOTA Simplified Responsive Grid - 2x2 for <1536px, 1x4 for ≥1536px */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-4 gap-6 max-w-7xl mx-auto animate-in fade-in duration-500">
        {Object.entries(data.plans).map(([planKey, plan]) => {
          const typedPlanKey = planKey as keyof typeof planStates;
          const planState = planStates[typedPlanKey];
          const isPremium = planKey !== 'standard';

          const getBorderStyle = () => {
            switch (planKey) {
              case 'pass':
                return 'border-blue-500 shadow-lg shadow-blue-500/20 hover:shadow-blue-500/30';
              case 'diamond':
                return 'border-purple-500 shadow-lg shadow-purple-500/20 hover:shadow-purple-500/30';
              case 'metaverse':
                return 'border-cyan-500 shadow-lg shadow-cyan-500/20 hover:shadow-cyan-500/30';
              default:
                return 'border-orange-500 shadow-lg shadow-orange-500/20 hover:shadow-orange-500/30';
            }
          };

          return (
            <div
              key={planKey}
              className={`relative bg-white dark:bg-gray-800 border rounded-xl p-6 pt-8 transition-all duration-300 h-full flex flex-col overflow-visible hover:scale-105 hover:-translate-y-1 ${getBorderStyle()}`}
              style={{ zIndex: planKey === 'diamond' || planKey === 'metaverse' ? 10 : 1 }}
            >
              {/* Simplified Premium Badges */}
              {planKey === 'diamond' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-30">
                  <div className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg border-2 border-white dark:border-gray-800 hover:scale-110 transition-transform">
                    <span className="flex items-center gap-1.5">
                      <Star className="w-4 h-4" fill="currentColor" />
                      <span>Most Popular</span>
                    </span>
                  </div>
                </div>
              )}
              {planKey === 'metaverse' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-30">
                  <div className="bg-gradient-to-r from-cyan-400 via-blue-500 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg border-2 border-white dark:border-gray-800 hover:scale-110 transition-transform">
                    <span className="flex items-center gap-1.5">
                      <Sparkles className="w-4 h-4" />
                      <span>Ultimate</span>
                    </span>
                  </div>
                </div>
              )}

              <div className={`text-center mb-6 ${(planKey === 'diamond' || planKey === 'metaverse') ? 'mt-4' : ''}`}>
                <h3 className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 leading-tight">{plan.name}</h3>
                <div className="mb-4">
                  {plan.price === 0 ? (
                    <span className="text-3xl font-bold text-gray-900 dark:text-gray-100">Free</span>
                  ) : (
                    <div>
                      <span className="text-3xl font-bold text-gray-900 dark:text-gray-100">${plan.price}</span>
                      <span className="text-gray-500 dark:text-gray-400">/{plan.period === 'month' ? 'month' : plan.period}</span>
                    </div>
                  )}
                </div>

                {/* Status indicator */}
                {planKey === 'standard' && (
                  <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                    Always Active
                  </div>
                )}
                {isPremium && planState.isActive && (
                  <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                    Active
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Valid until: {planState.expiresAt}
                    </div>
                  </div>
                )}
              </div>

              {/* Features section with flex-1 to push button to bottom */}
              <div className="flex-1 mb-6">
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-1.5">
                      <Check size={16} className="text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Button at bottom */}
              <button
                onClick={() => {
                  if (planKey === 'standard') return;
                  handleSubscriptionToggle(planKey as 'pass' | 'diamond' | 'metaverse');
                }}
                disabled={planKey === 'standard'}
                className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
                  planKey === 'standard'
                    ? 'bg-green-100 text-green-800 cursor-not-allowed dark:bg-green-900/30 dark:text-green-400'
                    : planState.isActive
                    ? 'bg-red-50 text-red-700 hover:bg-red-100 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/30'
                    : 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:shadow-lg hover:shadow-current/30 transform hover:scale-[1.02] active:scale-[0.98]'
                }`}
              >
                {planKey === 'standard'
                  ? 'Currently Active'
                  : planState.isActive
                  ? 'Cancel At Next Billing Cycle'
                  : 'Subscribe Now'
                }
              </button>
            </div>
          );
        })}
      </div>
      </div>
    );
};

export default SubscriptionSection; 