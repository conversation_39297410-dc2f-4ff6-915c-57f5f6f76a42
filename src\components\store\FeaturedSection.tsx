'use client';

import React, { useState } from 'react';
import { Crown, ArrowRight } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import FeaturedCard, { FeaturedCardItem } from './FeaturedCard';
import { featuredCardsMockData } from '@/data/featuredCardsMockData';

interface FeaturedSectionProps {
  lang: string;
}

const FeaturedSection: React.FC<FeaturedSectionProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [featuredItems, setFeaturedItems] = useState<FeaturedCardItem[]>(featuredCardsMockData);
  const [purchasingItemId, setPurchasingItemId] = useState<string | null>(null);

  const handlePurchase = async (item: FeaturedCardItem) => {
    try {
      setPurchasingItemId(item.id);

      // Simulate purchase process
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update the item as purchased
      setFeaturedItems(prev => prev.map(prevItem =>
        prevItem.id === item.id
          ? {
              ...prevItem,
              isPurchased: true,
              purchaseCount: (prevItem.purchaseCount || 0) + 1
            }
          : prevItem
      ));

      console.log('Purchase successful:', item.name);
    } catch (error) {
      console.error('Purchase failed:', error);
    } finally {
      setPurchasingItemId(null);
    }
  };





  return (
    <div className="space-y-6">

      {/* Featured Items Responsive Grid - Unified Layout */}
      <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
        {featuredItems.map((item) => (
          <FeaturedCard
            key={item.id}
            item={item}
            onPurchase={handlePurchase}
            isLoading={purchasingItemId === item.id}
            variant="responsive"
          />
        ))}
      </div>


      {/* Special 9.99 Subscription Promotion */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border border-indigo-200 dark:border-indigo-800 rounded-xl p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Crown className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                Premium Pass - Special Offer
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Unlock unlimited conversations and premium features for just $9.99/month
              </p>
            </div>
          </div>
          <button className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-current/30 transition-all duration-200 flex items-center gap-2">
            <Crown className="w-4 h-4" />
            Subscribe Now
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default FeaturedSection;
