'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON>, Gem, <PERSON>, Zap } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface RechargeOption {
  id: string;
  name: string;
  description: string;
  cost: number;
  currency: 'alphane' | 'endora';
  mindFuel: number | 'full';
  icon: React.ComponentType<any>;
  bgColor: string;
  popular?: boolean;
}

interface MindFuelRechargeModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentMindFuel: number;
  maxMindFuel: number;
  lang: string;
  onRecharge: (option: RechargeOption) => void;
}

const MindFuelRechargeModal: React.FC<MindFuelRechargeModalProps> = ({
  isOpen,
  onClose,
  currentMindFuel,
  maxMindFuel,
  lang,
  onRecharge
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  const rechargeOptions: RechargeOption[] = [
    {
      id: 'alphane_1',
      name: t('mindFuel.recharge.options.mindFuel1'),
      description: t('mindFuel.recharge.options.alphaneDescription'),
      cost: 100,
      currency: 'alphane',
      mindFuel: 1,
      icon: Heart,
      bgColor: 'from-red-400 to-pink-500'
    },
    {
      id: 'alphane_5',
      name: t('mindFuel.recharge.options.mindFuel5'),
      description: t('mindFuel.recharge.options.alphaneDescription'),
      cost: 450,
      currency: 'alphane',
      mindFuel: 5,
      icon: Heart,
      bgColor: 'from-red-400 to-pink-500',
      popular: true
    },
    {
      id: 'endora_1',
      name: t('mindFuel.recharge.options.mindFuel1'),
      description: t('mindFuel.recharge.options.endoraDescription'),
      cost: 10,
      currency: 'endora',
      mindFuel: 1,
      icon: Heart,
      bgColor: 'from-blue-400 to-cyan-500'
    },
    {
      id: 'endora_full',
      name: t('mindFuel.recharge.options.fullRestore'),
      description: t('mindFuel.recharge.options.fullDescription'),
      cost: 50,
      currency: 'endora',
      mindFuel: 'full',
      icon: Zap,
      bgColor: 'from-blue-400 to-cyan-500'
    }
  ];

  const handleConfirm = () => {
    const option = rechargeOptions.find(opt => opt.id === selectedOption);
    if (option) {
      onRecharge(option);
      onClose();
    }
  };

  const getMindFuelAfterRecharge = (option: RechargeOption) => {
    if (option.mindFuel === 'full') return maxMindFuel;
    return Math.min(currentMindFuel + (option.mindFuel as number), maxMindFuel);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* 模态框内容 */}
      <div className="relative bg-white dark:bg-gray-800 rounded-3xl p-6 mx-4 max-w-md w-full max-h-[80vh] overflow-y-auto shadow-2xl">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{t('mindFuel.recharge.title')}</h2>
          <button
            onClick={onClose}
            className="p-2 bg-gray-100 dark:bg-gray-700 rounded-xl hover:scale-110 transition-transform"
          >
            <X className="w-5 h-5 text-gray-600 dark:text-gray-300" />
          </button>
        </div>

        {/* 当前状态 */}
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-2xl p-4 mb-6">
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t('mindFuel.currentMindFuel')}</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {currentMindFuel} / {maxMindFuel}
            </p>
          </div>
        </div>

        {/* 充值选项 */}
        <div className="space-y-3 mb-6">
          {rechargeOptions.map((option) => {
            const IconComponent = option.icon;
            const CurrencyIcon = option.currency === 'alphane' ? Flame : Gem;
            const isSelected = selectedOption === option.id;
            const mindFuelAfter = getMindFuelAfterRecharge(option);
            
            return (
              <div
                key={option.id}
                className={`relative p-4 rounded-2xl border-2 cursor-pointer transition-all ${
                  isSelected
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
                onClick={() => setSelectedOption(option.id)}
              >
                {option.popular && (
                  <div className="absolute -top-2 left-4 px-2 py-1 bg-orange-500 text-white text-xs rounded-full">
                    推荐
                  </div>
                )}
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-12 h-12 bg-gradient-to-br ${option.bgColor} rounded-xl flex items-center justify-center`}>
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="font-bold text-gray-900 dark:text-gray-100">{option.name}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{option.description}</p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center gap-1 justify-end mb-1">
                      <CurrencyIcon className={`w-4 h-4 ${option.currency === 'alphane' ? 'text-orange-500' : 'text-blue-500'}`} />
                      <span className="font-bold text-gray-900 dark:text-gray-100">{option.cost}</span>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {currentMindFuel} → {mindFuelAfter}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* 确认按钮 */}
        <button
          onClick={handleConfirm}
          disabled={!selectedOption}
          className={`w-full py-4 rounded-2xl font-bold text-lg transition-all ${
            selectedOption
              ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white hover:scale-105 shadow-lg'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed'
          }`}
        >
          {selectedOption ? t('mindFuel.recharge.confirm') : t('mindFuel.recharge.rechargeOptions')}
        </button>
      </div>
    </div>
  );
};

export default MindFuelRechargeModal;
