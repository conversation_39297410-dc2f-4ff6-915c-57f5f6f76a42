import type { <PERSON>ada<PERSON> } from "next";
import localFont from "next/font/local";
import "../globals.css";
import { languages } from '../i18n/settings'
import { initTranslations } from "../i18n";
import I18nProvider from './i18n-provider';
import { Providers } from "@/components/Providers";
import { ThemeProvider } from "@/components/ThemeProvider";
import { Toaster } from 'react-hot-toast';

const i18nNamespaces = ['translation'];

export async function generateStaticParams() {
  return languages.map((lang) => ({ lang }))
}

const quicksand = localFont({
  src: "../../../public/fonts/Quicksand/Quicksand-VariableFont_wght.ttf",
  variable: "--font-quicksand",
  display: "swap",
});

const jkMaruGothic = localFont({
  src: "../../../public/fonts/jkmarugo/JK-Maru-Gothic-M.otf",
  variable: "--font-jk-maru",
  display: "swap",
});

const lansui = localFont({
  src: "../../../public/fonts/Iansui0.91-Regular-2.ttf",
  variable: "--font-lansui",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Alphane AI - Your Warm AI Companion",
  description: "Experience deep emotional connections with AI companions through immersive character interactions and personalized conversations.",
};

export default async function RootLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{
    lang: string;
  }>;
}>) {
  const { lang } = await params;
  const { resources } = await initTranslations(lang, i18nNamespaces);

  // 根据语言选择字体
  const fontVariables = lang === 'ja' 
    ? `${jkMaruGothic.variable} ${quicksand.variable} ${lansui.variable} font-japanese`
    : lang === 'zh'
    ? `${lansui.variable} ${quicksand.variable} ${jkMaruGothic.variable} font-chinese`
    : `${quicksand.variable} ${jkMaruGothic.variable} ${lansui.variable} font-english`;

  return (
    <div className={fontVariables}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <I18nProvider
          resources={resources}
          locale={lang}
          namespaces={i18nNamespaces}
        >
          <Providers>{children}</Providers>
          <Toaster
            position="top-center"
            toastOptions={{
              duration: 3000,
              style: {
                background: 'var(--background)',
                color: 'var(--foreground)',
                border: '1px solid var(--border)',
              },
            }}
          />
        </I18nProvider>
      </ThemeProvider>
    </div>
  );
}
