import { apiRequest, ApiResponse, LoginResponseData, User, authenticatedRequest } from './api';

// 发送OTP请求参数
export interface SendOtpRequest {
  email?: string;
  phone?: string;
  type: 'signin' | 'recovery';
  captcha: string;
}

// 登录请求参数
export interface LoginRequest {
  provider: 'password' | 'otp';
  email?: string;
  phone?: string;
  password?: string;
  otp?: string;
  invitor_id?: string;
  captcha: string;
}

// 重置密码请求参数
export interface ChangePasswordRequest {
  email?: string;
  phone?: string;
  otp: string;
  password: string;
}

// 发送OTP验证码
export async function sendOtp(data: SendOtpRequest): Promise<ApiResponse<null>> {
  return apiRequest('/auth/send-otp', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 登录/注册
export async function login(data: LoginRequest): Promise<ApiResponse<LoginResponseData>> {
  return apiRequest<LoginResponseData>('/auth/login', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 重置密码
export async function changePassword(data: ChangePasswordRequest): Promise<ApiResponse<null>> {
  return apiRequest('/auth/change-password', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 获取当前用户信息
export async function getCurrentUser(): Promise<ApiResponse<User>> {
  return authenticatedRequest<User>('/user/me');
}

// 通过UID获取用户信息
export async function getUserByUid(uid: string): Promise<ApiResponse<User>> {
  return apiRequest<User>(`/user/profile/${uid}`);
}

// 获取用户的关注列表
export async function getUserFollowing(uid: string): Promise<ApiResponse<User[]>> {
  return apiRequest<User[]>(`/user/${uid}/following`);
}

// 获取用户的粉丝列表
export async function getUserFollowers(uid: string): Promise<ApiResponse<User[]>> {
  return apiRequest<User[]>(`/user/${uid}/followers`);
}

// 获取用户的链接信息
export async function getUserLinks(uid: string): Promise<ApiResponse<any>> {
  return apiRequest<any>(`/user/${uid}/links`);
}

// 本地存储管理
export function saveAuthToken(token: string) {
  localStorage.setItem('token', token);
}

export function clearAuthTokens() {
  localStorage.removeItem('token');
}

export function getAccessToken(): string | null {
  return localStorage.getItem('token');
} 