'use client';

import React, { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';

interface User {
  stamina_current?: number;
  stamina_max?: number;
  stamina_recovery_time?: number; // 下次回复时间的时间戳
  membership_tier?: 'standard' | 'pass' | 'diamond' | 'metaverse';
}

interface SidebarStaminaDisplayProps {
  user: User | null;
  lang: string;
}

const SidebarStaminaDisplay: React.FC<SidebarStaminaDisplayProps> = ({ user, lang }) => {
  const router = useRouter();
  const { t } = useTranslation(lang, 'translation');
  const [isClient, setIsClient] = useState(false);

  // Ensure this only runs on client to avoid hydration mismatch
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 根据会员等级获取体力配置
  const getStaminaConfig = (membershipTier: string = 'standard') => {
    switch (membershipTier) {
      case 'pass':
        return {
          maxStamina: 20,
          recoverySpeed: 2, // 2倍回复速度
          recoveryInterval: 30 * 60 * 1000, // 30分钟回复1点
        };
      case 'diamond':
        return {
          maxStamina: 50,
          recoverySpeed: 5, // 5倍回复速度
          recoveryInterval: 12 * 60 * 1000, // 12分钟回复1点
        };
      case 'metaverse':
        return {
          maxStamina: Infinity,
          recoverySpeed: Infinity,
          recoveryInterval: 0,
        };
      default: // standard
        return {
          maxStamina: 10,
          recoverySpeed: 1,
          recoveryInterval: 60 * 60 * 1000, // 60分钟回复1点
        };
    }
  };

  const config = getStaminaConfig(user?.membership_tier);
  const currentStamina = user?.stamina_current || 8; // 默认值
  const maxStamina = config.maxStamina === Infinity ? '∞' : config.maxStamina;
  const recoveryTime = user?.stamina_recovery_time || Date.now() + 45 * 60 * 1000; // 默认45分钟后回复

  // 计算回复时间显示
  const getRecoveryTimeText = () => {
    if (!isClient) return '加载中...';
    
    if (config.maxStamina === Infinity) return t('stamina.unlimited') || '无限体力';
    if (currentStamina >= config.maxStamina) return t('stamina.full') || '已满';
    
    const now = Date.now();
    const timeLeft = recoveryTime - now;
    
    if (timeLeft <= 0) return t('stamina.aboutToRecover') || '即将回复';
    
    const minutes = Math.floor(timeLeft / (60 * 1000));
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  const handleClick = () => {
    router.push(`/${lang}/stamina`);
  };

  return (
    <div className="w-80 px-4">
      <div className="mb-1">
        <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
          {t('stamina.sidebar.title') || '体力'}
        </h3>
      </div>
      
      <div 
        className="flex items-center gap-3 cursor-pointer hover:opacity-80 transition-opacity"
        onClick={handleClick}
      >
        {/* 体力图标 - 与mobile header保持一致的风格 */}
        <div className="h-11 bg-red-500 rounded-lg flex items-center justify-center" style={{width: '3.625rem'}}>
          <Heart className="text-white fill-current" size={20}/>
        </div>
        
        {/* 体力信息 */}
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <span className="text-lg font-bold text-gray-800 dark:text-gray-100">
              {currentStamina}/{maxStamina}
            </span>
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {getRecoveryTimeText()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SidebarStaminaDisplay;
