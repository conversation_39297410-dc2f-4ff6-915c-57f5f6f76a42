'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Crown, Coins, Star, Gift, Cake, Palette, Heart } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';
import { StoreProvider } from '@/contexts/StoreContext';
import { ToastProvider } from '@/components/ui/Toast';
import FeaturedSection from '@/components/store/FeaturedSection';
import SubscriptionSection from '@/components/store/SubscriptionSection';
import WelcomeSection from '@/components/store/WelcomeSection';
import ArtsSection from '@/components/store/ArtsSection';
import MemorialSection from '@/components/store/MemorialSection';
import CurrencySection from '@/components/store/CurrencySection';
import MindFuelItemsSection from '@/components/store/MindFuelItemsSection';
import EnhancedTabNavigation, { TabItem } from '@/components/common/EnhancedTabNavigation';
import { getStoreTabColors } from '@/utils/tabColorSchemes';

interface Plan {
  name: string;
  price: number;
  period: string;
  status: 'active' | 'inactive' | 'permanent';
  features: string[];
}

interface SubscriptionData {
  currentPlans: {
    standard: { isActive: boolean; expiresAt: string | null };
    pass: { isActive: boolean; expiresAt: string | null };
    diamond: { isActive: boolean; expiresAt: string | null };
    metaverse: { isActive: boolean; expiresAt: string | null };
  };
  plans: {
    standard: Plan;
    pass: Plan;
    diamond: Plan;
    metaverse: Plan;
  };
}

interface StoreClientPageProps {
  lang: string;
}

const categories = [
  { id: 'featured', icon: Star, label: 'Featured', description: 'Top Picks & Deals' },
  { id: 'memberships', icon: Crown, label: 'MemberShip', description: 'Premium Plans' },
  { id: 'welcome', icon: Gift, label: 'Welcome', description: 'New User Offers' },
  { id: 'arts', icon: Palette, label: 'Arts', description: 'Character & Scenes' },
  { id: 'memorial', icon: Cake, label: 'Memorials', description: 'Special Events' },
  { id: 'currency', icon: Coins, label: 'Currency', description: 'Tokens & Credits' },
  { id: 'mindfuel', icon: Heart, label: 'Mind Fuel', description: 'Thought Energy Items' },
];

// Convert categories to TabItem format for enhanced navigation
const storeTabs: TabItem[] = categories.map(category => ({
  id: category.id,
  label: category.label,
  icon: category.icon,
  description: category.description
}));

const StoreClientPage: React.FC<StoreClientPageProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const searchParams = useSearchParams();
  const categoryParam = searchParams.get('category');

  const [activeCategory, setActiveCategory] = useState(categoryParam || 'featured');
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (categoryParam && categories.some(cat => cat.id === categoryParam)) {
      setActiveCategory(categoryParam);
    }
  }, [categoryParam]);

  const subscriptionData: SubscriptionData = {
    currentPlans: {
      standard: { isActive: true, expiresAt: null },
      pass: { isActive: false, expiresAt: null },
      diamond: { isActive: false, expiresAt: null },
      metaverse: { isActive: false, expiresAt: null }
    },
    plans: {
      standard: {
        name: 'Standard',
        price: 0,
        period: 'forever',
        status: 'permanent',
        features: [
          '5 AI conversations daily',
          'Basic character library access',
          'Standard quality avatars',
          'Community features'
        ]
      },
      pass: {
        name: 'Pass',
        price: 9.99,
        period: 'month',
        status: 'inactive',
        features: [
          'Unlimited AI conversations',
          'Full character library access',
          'High-definition avatars',
          'Priority customer support',
          'Exclusive badges',
          'Advanced customization options'
        ]
      },
      diamond: {
        name: 'Diamond',
        price: 29.99,
        period: 'month',
        status: 'inactive',
        features: [
          'All Pass features included',
          'Exclusive diamond characters',
          '4K ultra-HD avatars',
          'Private chat rooms',
          'Early access to new features',
          'Dedicated account manager',
          'Unlimited storage space'
        ]
      },
      metaverse: {
        name: 'MetaVerse Pass',
        price: 99.99,
        period: 'month',
        status: 'inactive',
        features: [
          'All Diamond features included',
          'MetaVerse world access',
          'Virtual reality integration',
          'Custom avatar creation',
          'Exclusive MetaVerse events',
          'Cross-platform synchronization',
          'Advanced AI interactions',
          'Virtual property ownership',
          'Premium MetaVerse content'
        ]
      }
    }
  };

  const renderCategoryContent = () => {
    switch (activeCategory) {
      case 'featured':
        return <FeaturedSection lang={lang} />;
      case 'memberships':
        return <SubscriptionSection lang={lang} subscriptionData={subscriptionData} />;
      case 'welcome':
        return <WelcomeSection lang={lang} />;
      case 'arts':
        return <ArtsSection lang={lang} searchQuery={searchQuery} />;
      case 'memorial':
        return <MemorialSection lang={lang} />;
      case 'currency':
        return <CurrencySection lang={lang} />;
      case 'mindfuel':
        return <MindFuelItemsSection lang={lang} />;
      default:
        return <FeaturedSection lang={lang} />;
    }
  };

  return (
    <ToastProvider>
      <StoreProvider>
        <MainAppLayout lang={lang} title={t('store.title')}>
          <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-slate-900 dark:to-gray-900">
            {/* Enhanced Tab Navigation */}
            <EnhancedTabNavigation
              tabs={storeTabs}
              activeTab={activeCategory}
              onTabChange={setActiveCategory}
              getTabColors={getStoreTabColors}
              containerMaxWidth="max-w-6xl"
            />

            {/* Tab Content */}
            <div className="space-y-6 p-3 sm:p-4 md:p-6">
              <div className="max-w-7xl mx-auto">
                {renderCategoryContent()}
              </div>
            </div>
          </div>
        </MainAppLayout>
      </StoreProvider>
    </ToastProvider>
  );
};

export default StoreClientPage;
