'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>rk<PERSON>, Crown, Heart, Coins, Clock, <PERSON>R<PERSON>, PartyPopper, Zap, X, Rocket, <PERSON>, Palette, Loader2, CheckCircle, Gift } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { WelcomeGift, ClaimGiftResponse, MockStoreService } from '@/lib/store-api';
import FirstTimePurchaseSection from './FirstTimePurchaseSection';
import { useGiftClaiming, useFirstTimePurchase } from '@/contexts/StoreContext';
import { useGiftToast } from '@/components/ui/Toast';

interface WelcomeSectionProps {
  lang: string;
}

interface WelcomeOffer {
  id: string;
  type: 'welcome' | 'first_recharge' | 'welcome_back' | 'first_popup' | 'version_limited' | 'ip_collab';
  title: string;
  description: string;
  rewards: {
    type: 'currency' | 'character' | 'subscription' | 'bonus' | 'feature' | 'exclusive';
    name: string;
    amount?: number;
    icon: React.ComponentType<any>;
    color: string;
  }[];
  requirements?: string;
  timeLimit?: string;
  claimed?: boolean;
  available: boolean;
  isPopup?: boolean;
  newVersion?: string;
  ipPartner?: string;
  priority?: number; // For popup ordering
}

interface ClaimState {
  isLoading: boolean;
  error: string | null;
  success: boolean;
}

const WelcomeSection: React.FC<WelcomeSectionProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [userType, setUserType] = useState<'new' | 'returning' | 'veteran'>('new'); // Mock user type
  const [lastLogin, setLastLogin] = useState<Date | null>(null);
  const [showPopup, setShowPopup] = useState<string | null>(null);
  const [dismissedPopups, setDismissedPopups] = useState<string[]>([]);

  // Use store context for gift claiming
  const { welcomeGifts, claimGift, claimStates } = useGiftClaiming();
  const { hasUserMadeFirstPurchase } = useFirstTimePurchase();
  const { showClaimSuccess, showClaimError } = useGiftToast();
  const [loading, setLoading] = useState(false); // Loading is now handled by context

  // Load user data
  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = () => {
    // Simulate determining user type
    const mockLastLogin = new Date();
    mockLastLogin.setDate(mockLastLogin.getDate() - 30); // 30 days ago
    setLastLogin(mockLastLogin);

    // Determine user type based on login history
    const daysSinceLastLogin = Math.floor((Date.now() - mockLastLogin.getTime()) / (1000 * 60 * 60 * 24));
    if (daysSinceLastLogin > 30) {
      setUserType('returning');
    } else if (daysSinceLastLogin > 7) {
      setUserType('veteran');
    } else {
      setUserType('new');
    }
  };



  // Helper functions - define first to avoid reference errors
  const getRewardIcon = (type: string) => {
    switch (type) {
      case 'currency': return Sparkles;
      case 'character': return Heart;
      case 'subscription': return Crown;
      case 'bonus': return Coins;
      case 'feature': return Zap;
      case 'exclusive': return Star;
      default: return Gift;
    }
  };

  const getRewardColor = (type: string) => {
    switch (type) {
      case 'currency': return 'from-blue-400 to-indigo-500';
      case 'character': return 'from-pink-400 to-rose-500';
      case 'subscription': return 'from-yellow-400 to-orange-500';
      case 'bonus': return 'from-emerald-400 to-green-500';
      case 'feature': return 'from-purple-400 to-pink-500';
      case 'exclusive': return 'from-orange-400 to-red-500';
      default: return 'from-gray-400 to-gray-500';
    }
  };

  const getPriorityByType = (type: string) => {
    switch (type) {
      case 'first_popup': return 1;
      case 'version_limited': return 2;
      case 'ip_collab': return 3;
      default: return 10;
    }
  };

  // Convert WelcomeGift to WelcomeOffer format for compatibility
  const welcomeOffers: WelcomeOffer[] = welcomeGifts.map(gift => ({
    id: gift.id,
    type: gift.type,
    title: gift.title,
    description: gift.description,
    rewards: gift.rewards.map(reward => ({
      type: reward.type,
      name: reward.name,
      amount: reward.amount,
      icon: getRewardIcon(reward.type),
      color: getRewardColor(reward.type)
    })),
    requirements: gift.requirements,
    timeLimit: gift.time_limit,
    claimed: gift.claimed,
    available: gift.available,
    isPopup: gift.type.includes('popup') || gift.type.includes('limited') || gift.type.includes('collab'),
    priority: getPriorityByType(gift.type)
  }));

  const availableOffers = welcomeOffers.filter(offer => offer.available);
  const regularOffers = availableOffers.filter(offer => !offer.isPopup);
  const popupOffers = availableOffers.filter(offer => offer.isPopup);

  // Popup management
  useEffect(() => {
    const sortedPopupOffers = popupOffers
      .filter(offer => offer.available)
      .sort((a, b) => (a.priority || 0) - (b.priority || 0));

    if (sortedPopupOffers.length > 0 && !showPopup) {
      setShowPopup(sortedPopupOffers[0].id);
    }
  }, [popupOffers, showPopup]);

  const handleClaimOffer = async (offerId: string) => {
    try {
      // Use context's claimGift function
      const response = await claimGift(offerId);

      if (response.success) {
        // Show success toast with rewards
        showClaimSuccess(response.rewards_granted);

        // Close popup if this was a popup offer
        if (showPopup === offerId) {
          setShowPopup(null);
        }
      }
    } catch (error) {
      showClaimError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const handleDismissPopup = (offerId: string) => {
    setDismissedPopups(prev => [...prev, offerId]);
    setShowPopup(null);
  };

  const getOfferIcon = (type: string) => {
    switch (type) {
      case 'welcome': return PartyPopper;
      case 'first_recharge': return Zap;
      case 'welcome_back': return Gift;
      case 'first_popup': return Rocket;
      case 'version_limited': return Zap;
      case 'ip_collab': return Palette;
      default: return Gift;
    }
  };

  const getOfferColor = (type: string) => {
    switch (type) {
      case 'welcome': return 'from-green-400 to-emerald-500';
      case 'first_recharge': return 'from-purple-400 to-pink-500';
      case 'welcome_back': return 'from-blue-400 to-indigo-500';
      default: return 'from-gray-400 to-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg flex items-center justify-center">
              <Loader2 className="w-5 h-5 text-white animate-spin" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Welcome Offers
            </h2>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Loading your exclusive offers...
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
              <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-xl mb-4"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded mb-4"></div>
              <div className="space-y-2 mb-6">
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
              </div>
              <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const currentPopup = showPopup ? welcomeOffers.find(offer => offer.id === showPopup) : null;

  return (
    <>
      {/* Popup Modal */}
      {currentPopup && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-2xl max-w-md w-full p-6 relative animate-in zoom-in-95 duration-200">
            {/* Close Button */}
            <button
              onClick={() => handleDismissPopup(currentPopup.id)}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>

            {/* Popup Content */}
            <div className="text-center mb-6">
              <div className={`w-16 h-16 bg-gradient-to-r ${getOfferColor(currentPopup.type)} rounded-full flex items-center justify-center mx-auto mb-4`}>
                {React.createElement(getOfferIcon(currentPopup.type), { className: "w-8 h-8 text-white" })}
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {currentPopup.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                {currentPopup.description}
              </p>
              {currentPopup.newVersion && (
                <div className="mt-2 inline-flex items-center gap-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs">
                  <Rocket className="w-3 h-3" />
                  Version {currentPopup.newVersion}
                </div>
              )}
              {currentPopup.ipPartner && (
                <div className="mt-2 inline-flex items-center gap-1 bg-pink-100 dark:bg-pink-900/50 text-pink-800 dark:text-pink-200 px-2 py-1 rounded-full text-xs">
                  <Palette className="w-3 h-3" />
                  {currentPopup.ipPartner}
                </div>
              )}
            </div>

            {/* Rewards */}
            <div className="space-y-3 mb-6">
              {currentPopup.rewards.map((reward, index) => {
                const RewardIcon = reward.icon;
                return (
                  <div key={index} className="flex items-center gap-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                    <div className={`w-8 h-8 bg-gradient-to-r ${reward.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
                      <RewardIcon className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {reward.name}
                    </span>
                  </div>
                );
              })}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <button
                onClick={() => handleDismissPopup(currentPopup.id)}
                className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Maybe Later
              </button>
              <button
                onClick={() => handleClaimOffer(currentPopup.id)}
                className="flex-1 py-2 px-4 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all"
              >
                Claim Now
              </button>
            </div>

            {currentPopup.timeLimit && (
              <div className="mt-3 text-center">
                <span className="text-xs text-orange-600 dark:text-orange-400 font-medium flex items-center justify-center gap-1">
                  <Clock className="w-3 h-3" />
                  {currentPopup.timeLimit}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="space-y-6">

      {/* Special Popup Offers Preview */}
      {popupOffers.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
            <Zap className="w-5 h-5 text-red-500" />
            Limited Time Specials
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {popupOffers.map((offer) => (
              <div
                key={offer.id}
                className="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 cursor-pointer hover:shadow-md transition-all"
                onClick={() => setShowPopup(offer.id)}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className={`w-8 h-8 bg-gradient-to-r ${getOfferColor(offer.type)} rounded-lg flex items-center justify-center`}>
                    {React.createElement(getOfferIcon(offer.type), { className: "w-4 h-4 text-white" })}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 text-sm truncate">
                      {offer.title}
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                      {offer.rewards.length} exclusive rewards
                    </p>
                  </div>
                </div>
                <button className="w-full bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs py-2 rounded-md hover:shadow-lg transition-all">
                  View Details
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Regular Welcome Offers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {regularOffers.map((offer) => {
          const OfferIcon = getOfferIcon(offer.type);
          
          return (
            <div
              key={offer.id}
              className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all duration-200 group relative overflow-hidden"
            >
              {/* Background decoration */}
              <div className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-br ${getOfferColor(offer.type)} opacity-10 rounded-full -translate-y-4 translate-x-4`} />
              
              {/* Header */}
              <div className="flex items-start justify-between mb-4 relative z-10">
                <div className={`w-12 h-12 bg-gradient-to-br ${getOfferColor(offer.type)} rounded-xl flex items-center justify-center shadow-lg`}>
                  <OfferIcon className="w-6 h-6 text-white" />
                </div>
                {offer.timeLimit && (
                  <div className="flex items-center gap-1 bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-200 px-2 py-1 rounded-full text-xs font-medium">
                    <Clock className="w-3 h-3" />
                    {offer.timeLimit}
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="mb-6 relative z-10">
                <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">
                  {offer.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  {offer.description}
                </p>
                
                {offer.requirements && (
                  <p className="text-xs text-blue-600 dark:text-blue-400 font-medium mb-4">
                    {offer.requirements}
                  </p>
                )}

                {/* Rewards */}
                <div className="space-y-2">
                  {offer.rewards.map((reward, index) => {
                    const RewardIcon = reward.icon;
                    return (
                      <div key={index} className="flex items-center gap-3">
                        <div className={`w-6 h-6 bg-gradient-to-br ${reward.color} rounded-lg flex items-center justify-center`}>
                          <RewardIcon className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                          {reward.name}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Action Button */}
              <button
                onClick={() => handleClaimOffer(offer.id)}
                disabled={offer.claimed || claimStates[offer.id]?.isLoading}
                className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center gap-2 relative z-10 transform ${
                  offer.claimed
                    ? 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200 cursor-not-allowed scale-95'
                    : claimStates[offer.id]?.isLoading
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed scale-95'
                    : `bg-gradient-to-r ${getOfferColor(offer.type)} text-white hover:shadow-lg hover:shadow-current/30 hover:scale-105 active:scale-95`
                }`}
              >
                {offer.claimed ? (
                  <div className="flex items-center gap-2 animate-in fade-in duration-500">
                    <CheckCircle className="w-4 h-4 animate-in zoom-in duration-300" />
                    <span>Claimed</span>
                  </div>
                ) : claimStates[offer.id]?.isLoading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Claiming...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 group-hover:gap-3 transition-all duration-200">
                    <Gift className="w-4 h-4" />
                    <span>Claim Rewards</span>
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
                  </div>
                )}
              </button>

              {/* Success Animation Overlay */}
              {claimStates[offer.id]?.success && (
                <div className="absolute inset-0 bg-green-500/20 rounded-xl flex items-center justify-center z-20 animate-in fade-in duration-500">
                  <div className="bg-green-500 text-white rounded-full p-3 animate-in zoom-in duration-300 delay-100">
                    <CheckCircle className="w-8 h-8" />
                  </div>
                </div>
              )}

              {/* Error Message */}
              {claimStates[offer.id]?.error && (
                <div className="mt-2 text-sm text-red-600 dark:text-red-400 text-center animate-in slide-in-from-top duration-300">
                  {claimStates[offer.id]?.error}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* First-Time Purchase Section */}
      {!hasUserMadeFirstPurchase && userType === 'new' && (
        <div className="mt-12">
          <FirstTimePurchaseSection
            lang={lang}
            onPurchase={(offerId) => {
              console.log('First-time purchase completed:', offerId);
            }}
          />
        </div>
      )}

      {/* Additional Info */}
      {userType === 'returning' && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <Heart className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <div>
              <h4 className="font-semibold text-blue-800 dark:text-blue-200">
                Welcome back to Alphane!
              </h4>
              <p className="text-sm text-blue-600 dark:text-blue-400">
                We've missed you! Your last visit was {lastLogin?.toLocaleDateString()}.
                Claim your welcome back rewards before they expire.
              </p>
            </div>
          </div>
        </div>
      )}
      </div>
    </>
  );
};

export default WelcomeSection;
