import { useState, useCallback } from 'react';
import { 
  login, 
  sendOtp, 
  saveAuthToken,
  LoginRequest,
  SendOtpRequest 
} from '@/lib/auth-api';
import { useAuthContext } from '@/components/AuthProvider';
import toast from 'react-hot-toast';

interface UseAuthState {
  isLoading: boolean;
  error: string | null;
}

interface UseAuthActions {
  loginUser: (data: Omit<LoginRequest, 'captcha'>) => Promise<void>;
  sendOtpCode: (data: Omit<SendOtpRequest, 'captcha'>) => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

export function useAuth(): UseAuthState & UseAuthActions {
  const { login: contextLogin, logout: contextLogout } = useAuthContext();
  const [state, setState] = useState<UseAuthState>({
    isLoading: false,
    error: null,
  });

  const setLoading = (isLoading: boolean) => {
    setState(prev => ({ ...prev, isLoading }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({ ...prev, error }));
  };

  // Get captcha token (simplified implementation, actual project needs Google Recaptcha integration)
  const getCaptchaToken = async (): Promise<string> => {
    // TODO: Actual project needs Google Recaptcha v2 integration
    // This returns a placeholder token
    return 'demo-captcha-token';
  };

  const loginUser = useCallback(async (data: Omit<LoginRequest, 'captcha'>) => {
    try {
      setLoading(true);
      setError(null);

      const captcha = await getCaptchaToken();
      const response = await login({ ...data, captcha });

      if (response.code === 200 || response.code === 201) {
        saveAuthToken(response.data.token);
        contextLogin(response.data.user);
        
        // Show success message
        if (response.code === 201) {
          toast.success('Registration successful! Welcome to Alphane.ai');
        } else {
          toast.success('Login successful!');
        }
      } else {
        const errorMessage = response.message || 'Login failed';
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed, please try again';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [contextLogin]);

  const sendOtpCode = useCallback(async (data: Omit<SendOtpRequest, 'captcha'>) => {
    try {
      setLoading(true);
      setError(null);

      const captcha = await getCaptchaToken();
      const response = await sendOtp({ ...data, captcha });

      if (response.code === 200) {
        toast.success('Verification code sent, please check');
      } else {
        const errorMessage = response.message || 'Failed to send verification code';
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Send OTP error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to send verification code, please try again';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = useCallback(() => {
    contextLogout();
    setError(null);
    toast.success('Logged out successfully');
  }, [contextLogout]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    ...state,
    loginUser,
    sendOtpCode,
    logout,
    clearError,
  };
} 