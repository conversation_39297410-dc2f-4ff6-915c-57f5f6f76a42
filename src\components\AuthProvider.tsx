'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@/lib/api';
import { getAccessToken, clearAuthTokens, getCurrentUser } from '@/lib/auth-api';
import { useRouter, usePathname } from 'next/navigation';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (user: User) => void;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  // 验证token并获取用户信息
  const refreshUser = async () => {
    try {
      const token = getAccessToken();
      if (!token) {
        setIsLoading(false);
        return;
      }

      const response = await getCurrentUser();
      if (response.code === 200) {
        setUser(response.data);
      } else {
        // Token可能已过期，清除本地存储
        clearAuthTokens();
        setUser(null);
      }
    } catch (error) {
      console.error('Error fetching user info:', error);
      // 如果API调用失败，清除本地存储
      clearAuthTokens();
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshUser();
  }, []);

  const login = (userData: User) => {
    setUser(userData);
    
    // 登录成功后跳转逻辑
    const isAuthPage = pathname?.includes('/auth');
    if (isAuthPage) {
      // 从认证页面跳转到首页
      const lang = pathname?.split('/')[1] || 'en';
      router.push(`/${lang}`);
    }
  };

  const logout = () => {
    clearAuthTokens();
    setUser(null);
    
    // 登出后跳转到认证页面
    const lang = pathname?.split('/')[1] || 'en';
    router.push(`/${lang}/auth`);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
} 