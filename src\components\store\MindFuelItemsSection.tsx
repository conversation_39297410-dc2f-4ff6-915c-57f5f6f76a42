'use client';

import React, { useState } from 'react';
import { Heart, Sparkles, Zap, Gift, ShoppingCart, Plus, Minus } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface MindSupplyItem {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  mindFuelRestore: number;
  price: number;
  currency: 'alphane' | 'endora';
  bgGradient: string;
  borderColor: string;
  glowColor: string;
  popular?: boolean;
  discount?: number;
}

interface MindFuelItemsSectionProps {
  lang: string;
  featured?: boolean;
}

const MindFuelItemsSection: React.FC<MindFuelItemsSectionProps> = ({ lang, featured = false }) => {
  const { t } = useTranslation(lang, 'translation');
  const [selectedQuantities, setSelectedQuantities] = useState<Record<string, number>>({});

  // Mind Fuel Supply Items Data
  const mindSupplyItems: MindSupplyItem[] = [
    {
      id: 'mind_supply_small',
      name: 'Small Mind Supply',
      description: 'Restores 1 Mind Fuel point',
      icon: Heart,
      rarity: 'common',
      mindFuelRestore: 1,
      price: 100,
      currency: 'alphane',
      bgGradient: 'from-pink-400 to-red-400',
      borderColor: 'border-pink-300',
      glowColor: 'shadow-pink-500/50'
    },
    {
      id: 'mind_supply_standard',
      name: 'Standard Mind Supply',
      description: 'Restores 3 Mind Fuel points',
      icon: Heart,
      rarity: 'rare',
      mindFuelRestore: 3,
      price: 280,
      currency: 'alphane',
      bgGradient: 'from-blue-400 to-purple-400',
      borderColor: 'border-blue-300',
      glowColor: 'shadow-blue-500/50',
      popular: true,
      discount: 10
    },
    {
      id: 'mind_supply_premium',
      name: 'Premium Mind Supply',
      description: 'Restores 5 Mind Fuel points',
      icon: Heart,
      rarity: 'epic',
      mindFuelRestore: 5,
      price: 450,
      currency: 'alphane',
      bgGradient: 'from-purple-400 to-indigo-400',
      borderColor: 'border-purple-300',
      glowColor: 'shadow-purple-500/50'
    },
    {
      id: 'mind_supply_perfect',
      name: 'Perfect Mind Supply',
      description: 'Fully restores Mind Fuel',
      icon: Sparkles,
      rarity: 'legendary',
      mindFuelRestore: 999,
      price: 50,
      currency: 'endora',
      bgGradient: 'from-yellow-400 via-orange-400 to-red-400',
      borderColor: 'border-yellow-300',
      glowColor: 'shadow-yellow-500/50'
    }
  ];

  const getRarityInfo = (rarity: string) => {
    switch (rarity) {
      case 'legendary':
        return {
          label: 'Legendary',
          textColor: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/50'
        };
      case 'epic':
        return {
          label: 'Epic',
          textColor: 'text-purple-600 dark:text-purple-400',
          bgColor: 'bg-purple-100 dark:bg-purple-900/50'
        };
      case 'rare':
        return {
          label: 'Rare',
          textColor: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-100 dark:bg-blue-900/50'
        };
      default:
        return {
          label: 'Common',
          textColor: 'text-gray-600 dark:text-gray-400',
          bgColor: 'bg-gray-100 dark:bg-gray-900/50'
        };
    }
  };

  const getCurrencyInfo = (currency: string) => {
    switch (currency) {
      case 'endora':
        return {
          name: 'Joy Crystals',
          symbol: <Sparkles className="w-4 h-4 text-blue-500" />,
          color: 'text-blue-600 dark:text-blue-400'
        };
      default:
        return {
          name: 'Glimmering Dust',
          symbol: <Zap className="w-4 h-4 text-orange-500" />,
          color: 'text-orange-600 dark:text-orange-400'
        };
    }
  };

  const handleQuantityChange = (itemId: string, change: number) => {
    setSelectedQuantities(prev => ({
      ...prev,
      [itemId]: Math.max(1, (prev[itemId] || 1) + change)
    }));
  };

  const handlePurchase = (item: MindSupplyItem) => {
    const quantity = selectedQuantities[item.id] || 1;
    const totalPrice = item.price * quantity * (item.discount ? (100 - item.discount) / 100 : 1);
    const currencyInfo = getCurrencyInfo(item.currency);

    console.log('Purchasing:', {
      item: item.name,
      quantity,
      totalPrice,
      currency: item.currency
    });

    alert(`Purchase successful!\n${item.name} x${quantity}\nCost: ${totalPrice} ${currencyInfo.name}`);
  };

  if (!featured) {
    return (
      <div className="space-y-6">

        {/* Items Grid - Unified Responsive Grid */}
        <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
          {mindSupplyItems.map((item) => {
            const Icon = item.icon;
            const rarityInfo = getRarityInfo(item.rarity);
            const currencyInfo = getCurrencyInfo(item.currency);
            const quantity = selectedQuantities[item.id] || 1;
            const finalPrice = item.price * (item.discount ? (100 - item.discount) / 100 : 1);

            return (
              <div
                key={item.id}
                className={`relative bg-white dark:bg-gray-800 rounded-2xl p-4 md:p-6 border-2 min-h-[400px] flex flex-col ${item.borderColor} hover:shadow-xl transition-all duration-300 group ${item.glowColor}`}
              >
                {/* Popular Badge */}
                {item.popular && (
                  <div className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                    Popular
                  </div>
                )}

                {/* Discount Badge */}
                {item.discount && (
                  <div className="absolute -top-2 -left-2 bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                    -{item.discount}%
                  </div>
                )}

                {/* Item Icon */}
                <div className={`w-16 h-16 bg-gradient-to-br ${item.bgGradient} rounded-2xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className="w-8 h-8 text-white" />
                </div>

                {/* Item Info - Flexible Content */}
                <div className="text-center mb-4 flex-1 flex flex-col">
                  <h3 className="text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
                    {item.name}
                  </h3>
                  <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                    {item.description}
                  </p>
                  
                  {/* Rarity Badge */}
                  <div className="mb-4">
                    <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${rarityInfo.bgColor} ${rarityInfo.textColor}`}>
                      {rarityInfo.label}
                    </span>
                  </div>
                </div>

                {/* Fixed Bottom Section */}
                <div className="mt-auto space-y-4">
                  {/* Quantity Selector */}
                  <div className="flex items-center justify-center gap-3">
                    <button
                      onClick={() => handleQuantityChange(item.id, -1)}
                      className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      disabled={quantity <= 1}
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="w-12 text-center font-semibold text-gray-900 dark:text-gray-100">
                      {quantity}
                    </span>
                    <button
                      onClick={() => handleQuantityChange(item.id, 1)}
                      className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>

                  {/* Price Display */}
                  <div className="text-center">
                    {item.discount && (
                      <div className="text-sm text-gray-500 dark:text-gray-400 line-through flex items-center justify-center gap-1">
                        {item.price * quantity} {currencyInfo.symbol}
                      </div>
                    )}
                    <div className={`text-lg sm:text-xl font-bold ${currencyInfo.color} flex items-center justify-center gap-1`}>
                      {Math.floor(finalPrice * quantity)} {currencyInfo.symbol}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {currencyInfo.name}
                    </div>
                  </div>

                  {/* Purchase Button */}
                  <button
                    onClick={() => handlePurchase(item)}
                    className={`w-full bg-gradient-to-r ${item.bgGradient} text-white font-semibold py-2.5 px-4 rounded-xl hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 group-hover:scale-105`}
                  >
                    <ShoppingCart className="w-4 h-4" />
                    Purchase
                  </button>
                </div>
              </div>
            );
          })}
        </div>

      </div>
    );
  }

  // Featured view (enhanced to match other cards)
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
        <Heart className="w-5 h-5 text-red-500" />
        Mind Fuel Supply Items
      </h3>
      <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
        {mindSupplyItems.slice(0, 4).map((item) => {
          const Icon = item.icon;
          const currencyInfo = getCurrencyInfo(item.currency);
          const finalPrice = item.price * (item.discount ? (100 - item.discount) / 100 : 1);
          const rarityInfo = getRarityInfo(item.rarity);

          return (
            <div
              key={item.id}
              className={`relative bg-white dark:bg-gray-800 rounded-xl p-4 border-2 min-h-[300px] flex flex-col ${item.borderColor} hover:shadow-lg transition-all duration-200 group`}
            >
              {/* Popular Badge */}
              {item.popular && (
                <div className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                  Popular
                </div>
              )}

              {/* Discount Badge */}
              {item.discount && (
                <div className="absolute -top-2 -left-2 bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                  -{item.discount}%
                </div>
              )}

              {/* Item Icon */}
              <div className={`w-12 h-12 bg-gradient-to-br ${item.bgGradient} rounded-xl flex items-center justify-center mb-3 mx-auto shadow-lg`}>
                <Icon className="w-6 h-6 text-white" />
              </div>

              {/* Content - Flexible */}
              <div className="flex-1 flex flex-col text-center">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
                  {item.name}
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                  {item.description}
                </p>
                
                {/* Rarity Badge */}
                <div className="mb-4">
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${rarityInfo.bgColor} ${rarityInfo.textColor}`}>
                    {rarityInfo.label}
                  </span>
                </div>

                {/* Fixed Bottom Section */}
                <div className="mt-auto space-y-3">
                  {/* Price Display */}
                  <div>
                    {item.discount && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 line-through flex items-center justify-center gap-1">
                        {item.price} {currencyInfo.symbol}
                      </div>
                    )}
                    <div className={`text-lg font-bold ${currencyInfo.color} flex items-center justify-center gap-1`}>
                      {Math.floor(finalPrice)} {currencyInfo.symbol}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {currencyInfo.name}
                    </div>
                  </div>

                  {/* Purchase Button */}
                  <button 
                    className={`w-full bg-gradient-to-r ${item.bgGradient} text-white font-medium py-2 px-3 rounded-lg text-sm hover:shadow-lg transition-all duration-200 flex items-center justify-center gap-1`}
                    onClick={() => handlePurchase(item)}
                  >
                    <Icon className="w-3 h-3" />
                    Purchase
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MindFuelItemsSection;
