import type { Config } from "tailwindcss";
import { fontFamily } from "tailwindcss/defaultTheme";

export default {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-quicksand)", ...fontFamily.sans],
        japanese: ["var(--font-jk-maru)", "var(--font-quicksand)", ...fontFamily.sans],
        english: ["var(--font-quicksand)", "var(--font-jk-maru)", ...fontFamily.sans],
        chinese: ["var(--font-lansui)", "var(--font-quicksand)", ...fontFamily.sans],
      },
      colors: {
        background: "rgb(var(--background))",
        foreground: {
          DEFAULT: "rgb(var(--foreground))",
          secondary: "rgb(var(--foreground-secondary))",
          muted: "rgb(var(--foreground-muted))",
        },
        card: {
          DEFAULT: "rgb(var(--card))",
          foreground: "rgb(var(--card-foreground))",
          secondary: "rgb(var(--card-secondary))",
        },
        popover: {
          DEFAULT: "rgb(var(--popover))",
          foreground: "rgb(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "rgb(var(--primary))",
          pink: "rgb(var(--primary-pink))",
          foreground: "rgb(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "rgb(var(--secondary))",
          pink: "rgb(var(--secondary-pink))",
          foreground: "rgb(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "rgb(var(--muted))",
          foreground: "rgb(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "rgb(var(--accent))",
          pink: "rgb(var(--accent-pink))",
          foreground: "rgb(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "rgb(var(--destructive))",
          foreground: "rgb(var(--destructive-foreground))",
        },
        success: {
          DEFAULT: "rgb(var(--success))",
          foreground: "rgb(var(--success-foreground))",
        },
        warning: {
          DEFAULT: "rgb(var(--warning))",
          foreground: "rgb(var(--warning-foreground))",
        },
        border: "rgb(var(--border))",
        input: "rgb(var(--input))",
        ring: "rgb(var(--ring))",
        logo: "rgb(var(--logo))",
        icon: "rgb(var(--icon))",
        "header-foreground": "rgb(var(--header-foreground))",
        romantic: {
          primary: "rgb(var(--primary))",
          pink: "rgb(var(--primary-pink))",
          accent: "rgb(var(--accent))",
          "accent-pink": "rgb(var(--accent-pink))",
          secondary: "rgb(var(--secondary))",
          "secondary-pink": "rgb(var(--secondary-pink))",
        },
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
      backgroundImage: {
        'romantic-gradient': 'var(--primary-gradient)',
        'romantic-secondary': 'var(--secondary-gradient)',
        'romantic-accent': 'var(--accent-gradient)',
        'romantic-bg': 'var(--background-gradient)',
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      container: {
        center: true,
        padding: {
          DEFAULT: "1rem",
          sm: "2rem",
          lg: "4rem",
          xl: "5rem",
          "2xl": "6rem",
        },
        screens: {
          sm: "640px",
          md: "768px",
          lg: "1024px",
          xl: "1280px",
          "2xl": "1536px",
          "3xl": "2560px",
        },
      },
      screens: {
        xs: "475px",
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1536px",
        "3xl": "2560px",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
  ],
} satisfies Config;
