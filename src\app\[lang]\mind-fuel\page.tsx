import MindFuelClientPage from './MindFuelClientPage';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n';

export default async function MindFuelPage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const { t } = await useTranslation(lang, 'translation');

  return (
    <MainAppLayout lang={lang} title={t('mindFuel.title')}>
      <div className="min-h-screen bg-gray-50/50 dark:bg-gray-900/50">
        <MindFuelClientPage lang={lang} />
      </div>
    </MainAppLayout>
  );
}
