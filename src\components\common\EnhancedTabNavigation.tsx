'use client';

import React from 'react';
import { LucideIcon } from 'lucide-react';
import { useHeaderOffset } from '@/hooks/useHeaderOffset';

export interface TabItem {
  id: string;
  label: string;
  icon: LucideIcon;
  description?: string;
}

export interface TabColors {
  active: string;
  shadow: string;
  glow: string;
  pulse: string;
}

interface EnhancedTabNavigationProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  getTabColors: (tabId: string) => TabColors;
  className?: string;
  containerMaxWidth?: string;
}

const EnhancedTabNavigation: React.FC<EnhancedTabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
  getTabColors,
  className = '',
  containerMaxWidth = 'max-w-6xl'
}) => {
  // Use dynamic header offset calculation with 0 gap (tight against header)
  const { topOffset } = useHeaderOffset(0); // 0 gap for tight positioning

  return (
    <div
      className={`sticky z-10 ${className}`}
      style={{ top: topOffset }}
    >
      {/* Zero padding container - 0.5rem only on left/right edges */}
      <div className="px-2 pt-2">
        <div className={`${containerMaxWidth} mx-auto w-full`}>
          <div className="relative">
            {/* Desktop Navigation - Icon + Text (≥1024px) */}
            <div className="hidden lg:flex">
              <div className="w-full flex backdrop-blur-2xl bg-gradient-to-r from-white/20 via-white/30 to-white/20 dark:from-black/20 dark:via-black/30 dark:to-black/20 rounded-3xl border border-white/40 dark:border-white/20 shadow-2xl shadow-purple-500/10">
                {tabs.map((tab, index) => {
                  const Icon = tab.icon;
                  const isActive = activeTab === tab.id;
                  const colors = getTabColors(tab.id);

                  return (
                    <button
                      key={tab.id}
                      onClick={() => onTabChange(tab.id)}
                      className={`relative flex items-center justify-center gap-2 py-3 transition-all duration-500 group flex-1 ${
                        index === 0 ? 'rounded-l-3xl' : ''
                      } ${
                        index === tabs.length - 1 ? 'rounded-r-3xl' : ''
                      } ${
                        isActive
                          ? `${colors.active} text-white ${colors.shadow} backdrop-blur-xl`
                          : 'text-foreground/80 hover:text-foreground hover:bg-white/20 dark:hover:bg-white/10 backdrop-blur-sm'
                      }`}
                    >
                      {/* Elegant glow effect for active tab */}
                      {isActive && (
                        <>
                          <div className={`absolute inset-0 bg-gradient-to-br ${colors.glow} ${
                            index === 0 ? 'rounded-l-3xl' : ''
                          } ${
                            index === tabs.length - 1 ? 'rounded-r-3xl' : ''
                          } blur-xl`}></div>
                          <div className={`absolute inset-0 bg-gradient-to-br ${colors.pulse} ${
                            index === 0 ? 'rounded-l-3xl' : ''
                          } ${
                            index === tabs.length - 1 ? 'rounded-r-3xl' : ''
                          } animate-pulse`}></div>
                        </>
                      )}

                      <div className="relative flex items-center justify-center gap-2">
                        <Icon className={`w-5 h-5 transition-all duration-300 ${
                          isActive ? 'scale-110 drop-shadow-lg' : 'group-hover:scale-105'
                        }`} />
                        <span className={`font-semibold text-sm transition-all duration-300 whitespace-nowrap text-center ${
                          isActive ? 'text-white drop-shadow-sm' : ''
                        }`}>
                          {tab.label}
                        </span>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Mobile Navigation - Icon Only (<1024px) */}
            <div className="lg:hidden">
              <div className="w-full flex backdrop-blur-2xl bg-gradient-to-r from-white/20 via-white/30 to-white/20 dark:from-black/20 dark:via-black/30 dark:to-black/20 rounded-2xl border border-white/40 dark:border-white/20 shadow-xl shadow-purple-500/10">
                {tabs.map((tab, index) => {
                  const Icon = tab.icon;
                  const isActive = activeTab === tab.id;
                  const colors = getTabColors(tab.id);

                  return (
                    <button
                      key={tab.id}
                      onClick={() => onTabChange(tab.id)}
                      className={`relative flex items-center justify-center py-3 transition-all duration-500 flex-1 ${
                        index === 0 ? 'rounded-l-2xl' : ''
                      } ${
                        index === tabs.length - 1 ? 'rounded-r-2xl' : ''
                      } ${
                        isActive
                          ? `${colors.active} text-white ${colors.shadow}`
                          : 'text-foreground/80 hover:text-foreground hover:bg-white/20 dark:hover:bg-white/10'
                      }`}
                    >
                      {/* Elegant glow effect for active tab */}
                      {isActive && (
                        <div className={`absolute inset-0 bg-gradient-to-br ${colors.glow} ${
                          index === 0 ? 'rounded-l-2xl' : ''
                        } ${
                          index === tabs.length - 1 ? 'rounded-r-2xl' : ''
                        } blur-lg`}></div>
                      )}

                      <div className="relative">
                        <Icon className={`w-5 h-5 transition-all duration-300 ${
                          isActive ? 'scale-110 drop-shadow-lg' : ''
                        }`} />
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedTabNavigation;
