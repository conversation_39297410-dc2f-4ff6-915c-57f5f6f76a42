import { TabColors } from '@/components/common/EnhancedTabNavigation';

// Gift page color schemes
export const getGiftTabColors = (tabId: string): TabColors => {
  switch (tabId) {
    case 'receive':
      return {
        active: 'bg-gradient-to-br from-purple-500/90 via-pink-500/90 to-purple-600/90',
        shadow: 'shadow-xl shadow-purple-500/30',
        glow: 'from-purple-400/20 via-pink-400/20 to-purple-500/20',
        pulse: 'from-purple-500/10 via-pink-500/10 to-purple-600/10'
      };
    case 'send':
      return {
        active: 'bg-gradient-to-br from-blue-500/90 via-indigo-500/90 to-purple-500/90',
        shadow: 'shadow-xl shadow-blue-500/30',
        glow: 'from-blue-400/20 via-indigo-400/20 to-purple-400/20',
        pulse: 'from-blue-500/10 via-indigo-500/10 to-purple-500/10'
      };
    case 'history':
      return {
        active: 'bg-gradient-to-br from-emerald-500/90 via-teal-500/90 to-cyan-500/90',
        shadow: 'shadow-xl shadow-emerald-500/30',
        glow: 'from-emerald-400/20 via-teal-400/20 to-cyan-400/20',
        pulse: 'from-emerald-500/10 via-teal-500/10 to-cyan-500/10'
      };
    default:
      return {
        active: 'bg-gradient-to-br from-purple-500/90 via-pink-500/90 to-purple-600/90',
        shadow: 'shadow-xl shadow-purple-500/30',
        glow: 'from-purple-400/20 via-pink-400/20 to-purple-500/20',
        pulse: 'from-purple-500/10 via-pink-500/10 to-purple-600/10'
      };
  }
};

// Wallet page color schemes
export const getWalletTabColors = (tabId: string): TabColors => {
  switch (tabId) {
    case 'overview':
      return {
        active: 'bg-gradient-to-br from-purple-500/90 via-pink-500/90 to-purple-600/90',
        shadow: 'shadow-xl shadow-purple-500/30',
        glow: 'from-purple-400/20 via-pink-400/20 to-purple-500/20',
        pulse: 'from-purple-500/10 via-pink-500/10 to-purple-600/10'
      };
    case 'orders':
      return {
        active: 'bg-gradient-to-br from-blue-500/90 via-indigo-500/90 to-blue-600/90',
        shadow: 'shadow-xl shadow-blue-500/30',
        glow: 'from-blue-400/20 via-indigo-400/20 to-blue-500/20',
        pulse: 'from-blue-500/10 via-indigo-500/10 to-blue-600/10'
      };
    case 'membership':
      return {
        active: 'bg-gradient-to-br from-amber-500/90 via-yellow-500/90 to-orange-500/90',
        shadow: 'shadow-xl shadow-amber-500/30',
        glow: 'from-amber-400/20 via-yellow-400/20 to-orange-400/20',
        pulse: 'from-amber-500/10 via-yellow-500/10 to-orange-500/10'
      };
    default:
      return {
        active: 'bg-gradient-to-br from-purple-500/90 via-pink-500/90 to-purple-600/90',
        shadow: 'shadow-xl shadow-purple-500/30',
        glow: 'from-purple-400/20 via-pink-400/20 to-purple-500/20',
        pulse: 'from-purple-500/10 via-pink-500/10 to-purple-600/10'
      };
  }
};

// Store page color schemes
export const getStoreTabColors = (tabId: string): TabColors => {
  switch (tabId) {
    case 'featured':
      return {
        active: 'bg-gradient-to-br from-purple-500/90 via-pink-500/90 to-purple-600/90',
        shadow: 'shadow-xl shadow-purple-500/30',
        glow: 'from-purple-400/20 via-pink-400/20 to-purple-500/20',
        pulse: 'from-purple-500/10 via-pink-500/10 to-purple-600/10'
      };
    case 'memberships':
      return {
        active: 'bg-gradient-to-br from-amber-500/90 via-yellow-500/90 to-orange-500/90',
        shadow: 'shadow-xl shadow-amber-500/30',
        glow: 'from-amber-400/20 via-yellow-400/20 to-orange-400/20',
        pulse: 'from-amber-500/10 via-yellow-500/10 to-orange-500/10'
      };
    case 'welcome':
      return {
        active: 'bg-gradient-to-br from-blue-500/90 via-indigo-500/90 to-purple-500/90',
        shadow: 'shadow-xl shadow-blue-500/30',
        glow: 'from-blue-400/20 via-indigo-400/20 to-purple-400/20',
        pulse: 'from-blue-500/10 via-indigo-500/10 to-purple-500/10'
      };
    case 'arts':
      return {
        active: 'bg-gradient-to-br from-emerald-500/90 via-teal-500/90 to-cyan-500/90',
        shadow: 'shadow-xl shadow-emerald-500/30',
        glow: 'from-emerald-400/20 via-teal-400/20 to-cyan-400/20',
        pulse: 'from-emerald-500/10 via-teal-500/10 to-cyan-500/10'
      };
    case 'memorial':
      return {
        active: 'bg-gradient-to-br from-rose-500/90 via-pink-500/90 to-red-500/90',
        shadow: 'shadow-xl shadow-rose-500/30',
        glow: 'from-rose-400/20 via-pink-400/20 to-red-400/20',
        pulse: 'from-rose-500/10 via-pink-500/10 to-red-500/10'
      };
    case 'currency':
      return {
        active: 'bg-gradient-to-br from-violet-500/90 via-purple-500/90 to-indigo-500/90',
        shadow: 'shadow-xl shadow-violet-500/30',
        glow: 'from-violet-400/20 via-purple-400/20 to-indigo-400/20',
        pulse: 'from-violet-500/10 via-purple-500/10 to-indigo-500/10'
      };
    case 'mindfuel':
      return {
        active: 'bg-gradient-to-br from-red-500/90 via-pink-500/90 to-rose-500/90',
        shadow: 'shadow-xl shadow-red-500/30',
        glow: 'from-red-400/20 via-pink-400/20 to-rose-400/20',
        pulse: 'from-red-500/10 via-pink-500/10 to-rose-500/10'
      };
    default:
      return {
        active: 'bg-gradient-to-br from-purple-500/90 via-pink-500/90 to-purple-600/90',
        shadow: 'shadow-xl shadow-purple-500/30',
        glow: 'from-purple-400/20 via-pink-400/20 to-purple-500/20',
        pulse: 'from-purple-500/10 via-pink-500/10 to-purple-600/10'
      };
  }
};

// Journey page color schemes
export const getJourneyTabColors = (tabId: string): TabColors => {
  switch (tabId) {
    case 'overview':
      return {
        active: 'bg-gradient-to-br from-purple-500/90 via-pink-500/90 to-purple-600/90',
        shadow: 'shadow-xl shadow-purple-500/30',
        glow: 'from-purple-400/20 via-pink-400/20 to-purple-500/20',
        pulse: 'from-purple-500/10 via-pink-500/10 to-purple-600/10'
      };
    case 'season':
      return {
        active: 'bg-gradient-to-br from-amber-500/90 via-orange-500/90 to-red-500/90',
        shadow: 'shadow-xl shadow-amber-500/30',
        glow: 'from-amber-400/20 via-orange-400/20 to-red-400/20',
        pulse: 'from-amber-500/10 via-orange-500/10 to-red-500/10'
      };
    case 'monthly':
      return {
        active: 'bg-gradient-to-br from-blue-500/90 via-indigo-500/90 to-purple-500/90',
        shadow: 'shadow-xl shadow-blue-500/30',
        glow: 'from-blue-400/20 via-indigo-400/20 to-purple-400/20',
        pulse: 'from-blue-500/10 via-indigo-500/10 to-purple-500/10'
      };
    case 'missions':
      return {
        active: 'bg-gradient-to-br from-emerald-500/90 via-teal-500/90 to-cyan-500/90',
        shadow: 'shadow-xl shadow-emerald-500/30',
        glow: 'from-emerald-400/20 via-teal-400/20 to-cyan-400/20',
        pulse: 'from-emerald-500/10 via-teal-500/10 to-cyan-500/10'
      };
    default:
      return {
        active: 'bg-gradient-to-br from-purple-500/90 via-pink-500/90 to-purple-600/90',
        shadow: 'shadow-xl shadow-purple-500/30',
        glow: 'from-purple-400/20 via-pink-400/20 to-purple-500/20',
        pulse: 'from-purple-500/10 via-pink-500/10 to-purple-600/10'
      };
  }
};

// Trophies page color schemes
export const getTrophyTabColors = (tabId: string): TabColors => {
  switch (tabId) {
    case 'overview':
      return {
        active: 'bg-gradient-to-br from-purple-500/90 via-pink-500/90 to-purple-600/90',
        shadow: 'shadow-xl shadow-purple-500/30',
        glow: 'from-purple-400/20 via-pink-400/20 to-purple-500/20',
        pulse: 'from-purple-500/10 via-pink-500/10 to-purple-600/10'
      };
    case 'achievements':
      return {
        active: 'bg-gradient-to-br from-amber-500/90 via-orange-500/90 to-red-500/90',
        shadow: 'shadow-xl shadow-amber-500/30',
        glow: 'from-amber-400/20 via-orange-400/20 to-red-400/20',
        pulse: 'from-amber-500/10 via-orange-500/10 to-red-500/10'
      };
    case 'explorations':
      return {
        active: 'bg-gradient-to-br from-emerald-500/90 via-teal-500/90 to-cyan-500/90',
        shadow: 'shadow-xl shadow-emerald-500/30',
        glow: 'from-emerald-400/20 via-teal-400/20 to-cyan-400/20',
        pulse: 'from-emerald-500/10 via-teal-500/10 to-cyan-500/10'
      };
    case 'social':
      return {
        active: 'bg-gradient-to-br from-blue-500/90 via-indigo-500/90 to-purple-500/90',
        shadow: 'shadow-xl shadow-blue-500/30',
        glow: 'from-blue-400/20 via-indigo-400/20 to-purple-400/20',
        pulse: 'from-blue-500/10 via-indigo-500/10 to-purple-500/10'
      };
    case 'ranking':
      return {
        active: 'bg-gradient-to-br from-rose-500/90 via-pink-500/90 to-fuchsia-500/90',
        shadow: 'shadow-xl shadow-rose-500/30',
        glow: 'from-rose-400/20 via-pink-400/20 to-fuchsia-400/20',
        pulse: 'from-rose-500/10 via-pink-500/10 to-fuchsia-500/10'
      };
    default:
      return {
        active: 'bg-gradient-to-br from-gray-500/90 via-slate-500/90 to-gray-600/90',
        shadow: 'shadow-xl shadow-gray-500/30',
        glow: 'from-gray-400/20 via-slate-400/20 to-gray-500/20',
        pulse: 'from-gray-500/10 via-slate-500/10 to-gray-600/10'
      };
  }
};
