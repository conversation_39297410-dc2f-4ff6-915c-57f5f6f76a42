'use client';

import React from 'react';
import { Search } from 'lucide-react';
import { useAuthContext } from '@/components/AuthProvider';
import { useResponsive } from '@/hooks/useResponsive';
import UnifiedSettingsDropdown from './UnifiedSettingsDropdown';
import { ComplexLogoWithRings, HorizontalCurrencyDisplay } from './MobileComponents';

interface ResponsiveHeaderProps {
  lang: string;
  onSearchClick: () => void;
  title?: string;
  notificationCenter?: React.ReactNode;
}

const ResponsiveHeader: React.FC<ResponsiveHeaderProps> = ({
  lang,
  onSearchClick,
  title,
  notificationCenter
}) => {
  const { user } = useAuthContext();
  const { isDesktop, isUltrawide } = useResponsive();

  // For all screens <1024px (mobile, tablet), use sidebar-style layout
  // Desktop layout should be used for ALL ≥1024px (desktop + ultrawide)
  const shouldUseDesktopLayout = isDesktop || isUltrawide;

  if (!shouldUseDesktopLayout) {
    return (
      <>
        <header className="sticky top-0 z-20 h-14 min-h-14 max-h-14 flex-shrink-0 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800">
          <div className="flex items-center justify-between h-full px-3">
            {/* Left: Progress Rings + Horizontal Currency */}
            <div className="flex items-center gap-2 flex-shrink-0 overflow-hidden">
              <ComplexLogoWithRings user={user} />

              {/* Horizontal Currency Display - logo width 1.6x, left-right layout */}
              <div className="flex-shrink min-w-0">
                <HorizontalCurrencyDisplay user={user} lang={lang} />
              </div>
            </div>

            {/* Right: Search and Settings */}
            <div className="flex items-center gap-1 flex-shrink-0">
              <button
                onClick={onSearchClick}
                className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-indigo-600 transition-colors rounded"
                aria-label="Search"
              >
                <Search size={24} />
              </button>
              <UnifiedSettingsDropdown lang={lang} />
            </div>
          </div>


        </header>
      </>
    );
  }

  // Desktop Header
  return (
    <header className="sticky top-0 z-20 h-16 min-h-16 max-h-16 flex-shrink-0 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800">
      <div className="flex items-center justify-between h-full px-4 lg:px-6">
        {/* Left: Search Input Box */}
        <div className="flex-1 max-w-5xl mr-4">
          <div 
            className="relative cursor-pointer"
            onClick={onSearchClick}
          >
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <div className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 select-none">
              Interesting moments and characters...
            </div>
          </div>
        </div>

        {/* Right: Action buttons */}
        <div className="flex items-center gap-2 flex-shrink-0">
          <button
            onClick={onSearchClick}
            className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-indigo-600 transition-colors rounded"
            aria-label="Search"
          >
            <Search size={28} />
          </button>
          {notificationCenter}
          <UnifiedSettingsDropdown lang={lang} />
        </div>
      </div>
    </header>
  );
};

export default ResponsiveHeader;
