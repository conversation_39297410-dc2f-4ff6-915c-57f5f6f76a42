// API配置
export const API_BASE_URL = 'https://api.alphane.ai/api/v1';

// 通用API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  detail: any;
}

// 用户信息类型
export interface User {
  _id: string;
  name: string;
  uid: string;
  email: string;
  avatar?: string;
  sign?: string;
  regions?: string;
  gender?: 'male' | 'female' | 'other';
  bio?: string;
  birthday?: string;
  age_verification_status?: 'not_verified' | 'pending' | 'verified';
  
  // 关注和订阅
  follow_count?: number;
  subscribe_count?: number;
  subscriber_count?: number;
  character_count?: number;
  story_count?: number;
  
  // 货币与代币
  alphane_dust_balance?: number;        // 曦光微尘
  endora_crystal_balance?: number;      // 心悦晶石
  serotile_fragment_balance?: number;   // 忆境拼图碎片
  oxytol_dew_balance?: number;          // 羁绊之露

  // 体力系统
  stamina_current?: number;             // 当前体力
  stamina_max?: number;                 // 最大体力
  stamina_recovery_time?: number;       // 下次回复时间戳
  membership_tier?: 'standard' | 'pass' | 'diamond' | 'metaverse'; // 会员等级
  
  // 激励与留存系统
  streak_current_days?: number;         // 当前连续互动天数
  streak_freeze_cards?: number;         // Streak Freeze卡数量
  daily_interaction_quota?: {
    fast_req_remaining: number;
    fast_req_total: number;
    slow_req_remaining: number;
    slow_req_total: number;
  };
  
  // 徽章和头像框
  displayed_honor_badges?: Array<{
    badge_id: string;
    name: string;
    icon_url: string;
  }>;
  owned_honor_badges?: Array<{
    badge_id: string;
    name: string;
    icon_url: string;
  }>;
  displayed_avatar_frame?: {
    frame_id: string;
    name: string;
    image_url: string;
  };
  owned_avatar_frames?: Array<{
    frame_id: string;
    name: string;
    image_url: string;
    source: string;
    description: string;
  }>;
  
  // 创作者与社区
  is_verified_creator?: boolean;
  creator_certification_level?: 'none' | 'new_star' | 'elite' | 'master';
  platform_contribution_points?: number;
  
  // 邀请系统
  invitor_id?: string;
  invitation_code?: string;
  
  // 会员信息
  alphane_pass_details?: {
    is_active: boolean;
    expires_at?: string;
    next_reward_at?: string;
  };
  alphane_diamond_details?: {
    is_active: boolean;
    expires_at?: string;
    next_reward_at?: string;
  };
  
  // 兼容性字段
  is_alphane_pass?: boolean; // 兼容旧版本，可以从alphane_pass_details.is_active推导
}

// 登录响应数据类型
export interface LoginResponseData {
  token: string;
  user: User;
  is_new_user: boolean;
  is_new_user_uid: string;
}

// 通用请求函数
export async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  const response = await fetch(url, {
    ...options,
    headers: defaultHeaders,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// 带认证的请求函数
export async function authenticatedRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const token = localStorage.getItem('token');
  
  const headers = {
    ...options.headers,
    ...(token && { Authorization: `Bearer ${token}` }),
  };

  return apiRequest<T>(endpoint, {
    ...options,
    headers,
  });
} 